#!/usr/bin/env elixir

# Simple script to test the coordinate transformation in the PDF analyzer

Mix.install([
  {:rustler, "~> 0.32.0"},
  {:jason, "~> 1.0"}
])

defmodule TestPdfAnalyzer do
  use Rustler,
    otp_app: :test_app,
    crate: "pdf_analyzer",
    mode: :release,
    load_from: {:wraft_doc, "priv/native/libpdf_analyzer"}

  def analyze_pdf_nif(_path, _target_fill_color, _target_stroke_color, _engine) do
    :erlang.nif_error(:nif_not_loaded)
  end

  def analyze_pdf(path, engine) do
    analyze_pdf_nif(path, nil, nil, engine)
  end

  def parse_result(json_string) do
    Jason.decode!(json_string)
  end
end

# Test the coordinate transformation
pdf_path = "/home/<USER>/workspace/wraft/organisations/653736e2-7c8f-4b57-bcad-ef3ed1056cc9/contents/VARZ0006/VARZ0006-v1.pdf"

IO.puts("Testing PDF coordinate transformation...")
IO.puts("PDF path: #{pdf_path}")

case TestPdfAnalyzer.analyze_pdf(pdf_path, "typst") do
  {:ok, json_result} ->
    result = TestPdfAnalyzer.parse_result(json_result)
    
    IO.puts("\n=== Analysis Results ===")
    IO.puts("Total pages: #{result["total_pages"]}")
    IO.puts("Total rectangles: #{result["total_rectangles"]}")
    
    rectangles = result["rectangles"]
    
    if length(rectangles) > 0 do
      IO.puts("\n=== First Rectangle (Coordinate Transformation Test) ===")
      rectangle = List.first(rectangles)
      
      position = rectangle["position"]
      corners = rectangle["corners"]
      dimensions = rectangle["dimensions"]
      
      IO.puts("Position: x=#{position["x"]}, y=#{position["y"]}")
      IO.puts("Dimensions: width=#{dimensions["width"]}, height=#{dimensions["height"]}")
      IO.puts("Corners: x1=#{corners["x1"]}, y1=#{corners["y1"]}, x2=#{corners["x2"]}, y2=#{corners["y2"]}")
      
      # Verify coordinate transformation
      IO.puts("\n=== Coordinate System Verification ===")
      
      if corners["y1"] <= corners["y2"] do
        IO.puts("✓ Y-coordinates follow bottom-left origin (y1 <= y2)")
      else
        IO.puts("✗ Y-coordinates do not follow bottom-left origin (y1 > y2)")
      end
      
      if position["y"] >= 0 and corners["y1"] >= 0 and corners["y2"] >= 0 do
        IO.puts("✓ All Y-coordinates are positive")
      else
        IO.puts("✗ Some Y-coordinates are negative")
      end
      
      expected_y2 = corners["y1"] + dimensions["height"]
      if abs(corners["y2"] - expected_y2) < 0.001 do
        IO.puts("✓ Corner coordinates are consistent with position and dimensions")
      else
        IO.puts("✗ Corner coordinates are inconsistent (expected y2=#{expected_y2}, got y2=#{corners["y2"]})")
      end
      
      IO.puts("\n=== All Rectangles Summary ===")
      Enum.with_index(rectangles, 1)
      |> Enum.each(fn {rect, index} ->
        pos = rect["position"]
        corn = rect["corners"]
        IO.puts("Rectangle #{index}: pos(#{pos["x"]}, #{pos["y"]}) corners(#{corn["x1"]}, #{corn["y1"]}, #{corn["x2"]}, #{corn["y2"]})")
      end)
    else
      IO.puts("No rectangles found in the PDF")
    end
    
  {:error, reason} ->
    IO.puts("Error analyzing PDF: #{reason}")
end
