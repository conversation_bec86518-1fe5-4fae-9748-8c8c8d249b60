defmodule WraftDoc.PdfAnalyzerTest do
  @moduledoc """
  Tests for PDF analyzer functionality, particularly coordinate system transformations.
  """
  use WraftDoc.DataCase, async: true

  alias WraftDoc.PdfAnalyzer

  describe "analyze_pdf/2" do
    test "analyzes PDF with typst engine and transforms coordinates to bottom-left origin" do
      pdf_path = "test/helper/invoice.pdf"
      
      case PdfAnalyzer.analyze_pdf(pdf_path, "typst") do
        {:ok, json_result} ->
          result = PdfAnalyzer.parse_result(json_result)
          
          # Verify the result structure
          assert is_map(result)
          assert Map.has_key?(result, "rectangles")
          assert Map.has_key?(result, "total_pages")
          assert Map.has_key?(result, "total_rectangles")
          
          # Check that rectangles have the expected coordinate structure
          rectangles = result["rectangles"]
          
          if length(rectangles) > 0 do
            rectangle = List.first(rectangles)
            
            # Verify rectangle structure
            assert Map.has_key?(rectangle, "position")
            assert Map.has_key?(rectangle, "dimensions")
            assert Map.has_key?(rectangle, "corners")
            
            # Verify position has x and y coordinates
            position = rectangle["position"]
            assert Map.has_key?(position, "x")
            assert Map.has_key?(position, "y")
            
            # Verify corners have all four coordinates
            corners = rectangle["corners"]
            assert Map.has_key?(corners, "x1")
            assert Map.has_key?(corners, "y1")
            assert Map.has_key?(corners, "x2")
            assert Map.has_key?(corners, "y2")
            
            # Verify coordinate transformation logic
            # For bottom-left origin: y1 should be <= y2 (y increases upward)
            assert corners["y1"] <= corners["y2"]
            
            # Verify that y coordinates are positive (assuming standard page dimensions)
            assert position["y"] >= 0
            assert corners["y1"] >= 0
            assert corners["y2"] >= 0
          end
          
        {:error, reason} ->
          # If the PDF can't be analyzed, that's okay for this test
          # We're mainly testing that the code compiles and runs
          IO.puts("PDF analysis failed (expected for some test PDFs): #{reason}")
      end
    end

    test "analyzes PDF with latex engine" do
      pdf_path = "test/helper/invoice.pdf"
      
      case PdfAnalyzer.analyze_pdf(pdf_path, "latex") do
        {:ok, json_result} ->
          result = PdfAnalyzer.parse_result(json_result)
          
          # Verify the result structure
          assert is_map(result)
          assert Map.has_key?(result, "rectangles")
          assert Map.has_key?(result, "total_pages")
          assert Map.has_key?(result, "total_rectangles")
          
        {:error, reason} ->
          # If the PDF can't be analyzed, that's okay for this test
          IO.puts("PDF analysis failed (expected for some test PDFs): #{reason}")
      end
    end

    test "handles invalid PDF path gracefully" do
      invalid_path = "test/helper/nonexistent.pdf"
      
      case PdfAnalyzer.analyze_pdf(invalid_path, "typst") do
        {:error, reason} ->
          assert is_binary(reason)
          assert String.contains?(reason, "Failed to open PDF")
          
        {:ok, _} ->
          flunk("Expected error for invalid PDF path")
      end
    end
  end

  describe "parse_result/1" do
    test "parses valid JSON result" do
      json_string = """
      {
        "total_pages": 1,
        "total_rectangles": 2,
        "rectangles": [
          {
            "operation": 0,
            "position": {"x": 10.0, "y": 20.0},
            "dimensions": {"width": 100.0, "height": 50.0},
            "corners": {"x1": 10.0, "y1": 20.0, "x2": 110.0, "y2": 70.0},
            "fill_color": "RGB(255, 255, 255)",
            "stroke_color": "RGB(0, 0, 0)",
            "line_width": 1.0,
            "border": 1.0,
            "font_name": null,
            "operation_type": "Fill only",
            "fill_color_operands": [1.0, 1.0, 1.0],
            "page": 1
          }
        ]
      }
      """
      
      result = PdfAnalyzer.parse_result(json_string)
      
      assert is_map(result)
      assert result["total_pages"] == 1
      assert result["total_rectangles"] == 2
      assert length(result["rectangles"]) == 1
      
      rectangle = List.first(result["rectangles"])
      assert rectangle["position"]["x"] == 10.0
      assert rectangle["position"]["y"] == 20.0
      assert rectangle["dimensions"]["width"] == 100.0
      assert rectangle["dimensions"]["height"] == 50.0
    end
  end
end
